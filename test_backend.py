#!/usr/bin/env python3
"""
Test script to debug the backend issues
"""

import sys
import traceback

print("🔍 Starting backend diagnostics...")

# Test 1: Basic imports
try:
    print("✅ Testing basic imports...")
    import os
    import json
    import time
    print("✅ Basic imports successful")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")
    sys.exit(1)

# Test 2: Flask imports
try:
    print("✅ Testing Flask imports...")
    from flask import Flask, request, jsonify
    from flask_cors import CORS
    print("✅ Flask imports successful")
except Exception as e:
    print(f"❌ Flask imports failed: {e}")
    print("📦 Install with: pip install flask flask-cors")

# Test 3: Hugging Face imports
try:
    print("✅ Testing Hugging Face imports...")
    from transformers.pipelines import pipeline
    from transformers import AutoTokenizer, AutoModel
    print("✅ Transformers imports successful")
except Exception as e:
    print(f"❌ Transformers imports failed: {e}")
    print("📦 Install with: pip install transformers")

try:
    from sentence_transformers import SentenceTransformer
    print("✅ Sentence Transformers imports successful")
except Exception as e:
    print(f"❌ Sentence Transformers imports failed: {e}")
    print("📦 Install with: pip install sentence-transformers")

try:
    from huggingface_hub import login
    print("✅ Hugging Face Hub imports successful")
except Exception as e:
    print(f"❌ Hugging Face Hub imports failed: {e}")
    print("📦 Install with: pip install huggingface-hub")

# Test 4: Authentication
try:
    print("✅ Testing Hugging Face authentication...")
    HF_TOKEN = "*************************************"
    
    # Try login
    try:
        from huggingface_hub import login
        login(token=HF_TOKEN)
        print("✅ Hugging Face login successful")
    except Exception as login_error:
        print(f"⚠️ Login failed: {login_error}")
        # Set environment variables as fallback
        os.environ["HUGGINGFACE_HUB_TOKEN"] = HF_TOKEN
        os.environ["HF_TOKEN"] = HF_TOKEN
        print("✅ Environment variables set as fallback")
        
except Exception as e:
    print(f"❌ Authentication setup failed: {e}")

# Test 5: Simple model loading
try:
    print("✅ Testing simple model loading...")
    from transformers.pipelines import pipeline
    
    # Try loading a simple sentiment model
    sentiment_model = pipeline("sentiment-analysis", return_all_scores=True)
    print("✅ Sentiment model loaded successfully")
    
    # Test the model
    test_result = sentiment_model("This is a great wine store!")
    print(f"✅ Model test successful: {test_result}")
    
except Exception as e:
    print(f"❌ Model loading failed: {e}")
    print(f"📋 Full error: {traceback.format_exc()}")

# Test 6: Check if abcd.py exists
try:
    print("✅ Testing abcd.py imports...")
    from abcd import extract_with_requests, scrape_duckduckgo, clean_url
    print("✅ abcd.py imports successful")
except Exception as e:
    print(f"❌ abcd.py imports failed: {e}")

# Test 7: Check if business_scraper.py exists
try:
    print("✅ Testing business_scraper.py imports...")
    from business_scraper import get_cache_key, get_cached_data, save_to_cache
    print("✅ business_scraper.py imports successful")
except Exception as e:
    print(f"❌ business_scraper.py imports failed: {e}")

print("🎉 Diagnostics complete!")
