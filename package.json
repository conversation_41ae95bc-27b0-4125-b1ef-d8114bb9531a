{"name": "nashira-wine-scraper", "version": "1.0.0", "description": "Nashira - From Daru to Dolce Vita - Wine Scraper with AI-powered recommendations", "scripts": {"dev": "cd scraper_king && npm run dev", "build": "cd scraper_king && npm run build", "start": "cd scraper_king && npm start", "backend": "python flask_backend.py", "install-frontend": "cd scraper_king && npm install", "install-all": "npm install && cd scraper_king && npm install"}, "devDependencies": {"@types/node": "22.15.30", "@types/react": "19.1.6", "typescript": "5.8.3"}}